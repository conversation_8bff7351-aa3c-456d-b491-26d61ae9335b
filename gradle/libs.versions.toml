[versions]
# Build tools
agp = "8.10.1"
kotlin = "2.0.21"

# AndroidX Core
coreKtx = "1.13.1"
lifecycleRuntimeKtx = "2.7.0"
activityCompose = "1.9.0"

# Compose
composeBom = "2024.09.00"
composeNavigation = "2.7.6"
composeViewModel = "2.7.0"
materialIconsExtended = "1.6.0"

# Coroutines
coroutines = "1.7.3"

# Testing
junit = "4.13.2"
junitVersion = "1.1.5"
espressoCore = "3.5.1"

# Other
jetbrainsKotlinJvm = "2.0.21"

[libraries]
# AndroidX Core
androidx-core-ktx = { group = "androidx.core", name = "core-ktx", version.ref = "coreKtx" }
androidx-lifecycle-runtime-ktx = { group = "androidx.lifecycle", name = "lifecycle-runtime-ktx", version.ref = "lifecycleRuntimeKtx" }
androidx-activity-compose = { group = "androidx.activity", name = "activity-compose", version.ref = "activityCompose" }

# Compose BOM and UI
androidx-compose-bom = { group = "androidx.compose", name = "compose-bom", version.ref = "composeBom" }
androidx-ui = { group = "androidx.compose.ui", name = "ui" }
androidx-ui-graphics = { group = "androidx.compose.ui", name = "ui-graphics" }
androidx-ui-tooling = { group = "androidx.compose.ui", name = "ui-tooling" }
androidx-ui-tooling-preview = { group = "androidx.compose.ui", name = "ui-tooling-preview" }
androidx-ui-test-manifest = { group = "androidx.compose.ui", name = "ui-test-manifest" }
androidx-ui-test-junit4 = { group = "androidx.compose.ui", name = "ui-test-junit4" }
androidx-material3 = { group = "androidx.compose.material3", name = "material3" }

# Compose Navigation and ViewModel
androidx-navigation-compose = { group = "androidx.navigation", name = "navigation-compose", version.ref = "composeNavigation" }
androidx-lifecycle-viewmodel-compose = { group = "androidx.lifecycle", name = "lifecycle-viewmodel-compose", version.ref = "composeViewModel" }
androidx-compose-material-icons-extended = { group = "androidx.compose.material", name = "material-icons-extended", version.ref = "materialIconsExtended" }

# Coroutines
kotlinx-coroutines-core = { group = "org.jetbrains.kotlinx", name = "kotlinx-coroutines-core", version.ref = "coroutines" }
kotlinx-coroutines-android = { group = "org.jetbrains.kotlinx", name = "kotlinx-coroutines-android", version.ref = "coroutines" }

# Testing
junit = { group = "junit", name = "junit", version.ref = "junit" }
androidx-junit = { group = "androidx.test.ext", name = "junit", version.ref = "junitVersion" }
androidx-espresso-core = { group = "androidx.test.espresso", name = "espresso-core", version.ref = "espressoCore" }

[plugins]
android-application = { id = "com.android.application", version.ref = "agp" }
android-library = { id = "com.android.library", version.ref = "agp" }
kotlin-android = { id = "org.jetbrains.kotlin.android", version.ref = "kotlin" }
kotlin-compose = { id = "org.jetbrains.kotlin.plugin.compose", version.ref = "kotlin" }
jetbrains-kotlin-jvm = { id = "org.jetbrains.kotlin.jvm", version.ref = "jetbrainsKotlinJvm" }

[bundles]
# 核心Android依赖包
android-core = [
    "androidx-core-ktx",
    "androidx-lifecycle-runtime-ktx"
]

# Compose基础依赖包
compose-core = [
    "androidx-ui",
    "androidx-ui-graphics",
    "androidx-ui-tooling-preview",
    "androidx-material3"
]

# Compose扩展依赖包
compose-extended = [
    "androidx-navigation-compose",
    "androidx-lifecycle-viewmodel-compose",
    "androidx-compose-material-icons-extended"
]

# 协程依赖包
coroutines = [
    "kotlinx-coroutines-core",
    "kotlinx-coroutines-android"
]

# 测试依赖包
testing-unit = [
    "junit"
]

# Android测试依赖包
testing-android = [
    "androidx-junit",
    "androidx-espresso-core"
]

# Compose测试依赖包
testing-compose = [
    "androidx-ui-test-junit4"
]

# Debug依赖包
debug-compose = [
    "androidx-ui-tooling",
    "androidx-ui-test-manifest"
]

